#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海豚湾密码管理器 - 主程序
类似于Bitwarden的密码管理软件
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                             QListWidget, QListWidgetItem, QDialog, QFormLayout,
                             QTextEdit, QCheckBox, QMessageBox, QTabWidget,
                             QSplitter, QFrame, QScrollArea, QGridLayout,
                             QComboBox, QSpinBox, QSlider, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize
from PyQt5.QtGui import QIcon, QFont, QPixmap, QPalette, QColor, QMovie

from password_manager import PasswordManager
from crypto_utils import CryptoManager
from ui_components import (LoginDialog, PasswordItemWidget, AddPasswordDialog, 
                          PasswordGeneratorDialog, SettingsDialog)


class DolphinPasswordManager(QMainWindow):
    """海豚湾密码管理器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.password_manager = PasswordManager()
        self.crypto_manager = CryptoManager()
        self.current_user = None
        self.is_locked = True
        
        self.init_ui()
        self.setup_connections()
        self.apply_theme()
        
        # 显示登录对话框
        self.show_login_dialog()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🐬 海豚湾密码管理器")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧边栏
        self.create_sidebar(splitter)
        
        # 右侧主内容区
        self.create_main_content(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
    
    def create_sidebar(self, parent):
        """创建左侧边栏"""
        sidebar = QFrame()
        sidebar.setFrameStyle(QFrame.StyledPanel)
        sidebar.setMaximumWidth(350)
        sidebar.setMinimumWidth(250)
        
        layout = QVBoxLayout(sidebar)
        
        # 用户信息区域
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.StyledPanel)
        user_layout = QVBoxLayout(user_frame)
        
        self.user_label = QLabel("未登录")
        self.user_label.setAlignment(Qt.AlignCenter)
        self.user_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px;")
        user_layout.addWidget(self.user_label)
        
        # 锁定/解锁按钮
        self.lock_button = QPushButton("🔒 锁定")
        self.lock_button.clicked.connect(self.toggle_lock)
        user_layout.addWidget(self.lock_button)
        
        layout.addWidget(user_frame)
        
        # 搜索框
        search_frame = QFrame()
        search_layout = QVBoxLayout(search_frame)
        
        search_label = QLabel("🔍 搜索密码")
        search_label.setStyleSheet("font-weight: bold; margin: 5px;")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入网站名称或用户名...")
        self.search_input.textChanged.connect(self.filter_passwords)
        search_layout.addWidget(self.search_input)
        
        layout.addWidget(search_frame)
        
        # 分类标签
        categories_frame = QFrame()
        categories_layout = QVBoxLayout(categories_frame)
        
        categories_label = QLabel("📁 分类")
        categories_label.setStyleSheet("font-weight: bold; margin: 5px;")
        categories_layout.addWidget(categories_label)
        
        self.category_list = QListWidget()
        self.category_list.addItems(["全部", "收藏夹", "最近使用", "网站", "应用", "信用卡", "身份信息", "安全备注"])
        self.category_list.currentTextChanged.connect(self.filter_by_category)
        categories_layout.addWidget(self.category_list)
        
        layout.addWidget(categories_frame)
        
        # 快捷操作按钮
        actions_frame = QFrame()
        actions_layout = QVBoxLayout(actions_frame)
        
        self.add_password_btn = QPushButton("➕ 添加密码")
        self.add_password_btn.clicked.connect(self.show_add_password_dialog)
        actions_layout.addWidget(self.add_password_btn)
        
        self.generate_password_btn = QPushButton("🎲 生成密码")
        self.generate_password_btn.clicked.connect(self.show_password_generator)
        actions_layout.addWidget(self.generate_password_btn)
        
        self.import_btn = QPushButton("📥 导入数据")
        self.import_btn.clicked.connect(self.import_passwords)
        actions_layout.addWidget(self.import_btn)
        
        self.export_btn = QPushButton("📤 导出数据")
        self.export_btn.clicked.connect(self.export_passwords)
        actions_layout.addWidget(self.export_btn)
        
        layout.addWidget(actions_frame)
        
        # 设置按钮
        self.settings_btn = QPushButton("⚙️ 设置")
        self.settings_btn.clicked.connect(self.show_settings)
        layout.addWidget(self.settings_btn)
        
        layout.addStretch()
        parent.addWidget(sidebar)
    
    def create_main_content(self, parent):
        """创建右侧主内容区"""
        main_content = QFrame()
        layout = QVBoxLayout(main_content)
        
        # 顶部工具栏
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        self.view_mode_label = QLabel("📋 密码库")
        self.view_mode_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        toolbar_layout.addWidget(self.view_mode_label)
        
        toolbar_layout.addStretch()
        
        # 视图切换按钮
        self.list_view_btn = QPushButton("📄")
        self.list_view_btn.setToolTip("列表视图")
        self.grid_view_btn = QPushButton("⊞")
        self.grid_view_btn.setToolTip("网格视图")
        
        toolbar_layout.addWidget(self.list_view_btn)
        toolbar_layout.addWidget(self.grid_view_btn)
        
        layout.addWidget(toolbar_frame)
        
        # 密码列表区域
        self.password_scroll = QScrollArea()
        self.password_scroll.setWidgetResizable(True)
        self.password_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.password_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.password_container = QWidget()
        self.password_layout = QVBoxLayout(self.password_container)
        self.password_layout.setAlignment(Qt.AlignTop)
        
        self.password_scroll.setWidget(self.password_container)
        layout.addWidget(self.password_scroll)
        
        parent.addWidget(main_content)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        
        # 密码数量标签
        self.password_count_label = QLabel("密码数量: 0")
        status_bar.addWidget(self.password_count_label)
        
        status_bar.addPermanentWidget(QLabel("🐬 海豚湾密码管理器 v1.0"))
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        file_menu.addAction('新建密码', self.show_add_password_dialog)
        file_menu.addSeparator()
        file_menu.addAction('导入', self.import_passwords)
        file_menu.addAction('导出', self.export_passwords)
        file_menu.addSeparator()
        file_menu.addAction('退出', self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')
        edit_menu.addAction('搜索', lambda: self.search_input.setFocus())
        edit_menu.addAction('生成密码', self.show_password_generator)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        view_menu.addAction('锁定', self.toggle_lock)
        view_menu.addAction('设置', self.show_settings)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        help_menu.addAction('关于', self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        toolbar.addAction('➕', self.show_add_password_dialog).setToolTip('添加密码')
        toolbar.addAction('🎲', self.show_password_generator).setToolTip('生成密码')
        toolbar.addSeparator()
        toolbar.addAction('📥', self.import_passwords).setToolTip('导入')
        toolbar.addAction('📤', self.export_passwords).setToolTip('导出')
        toolbar.addSeparator()
        toolbar.addAction('🔒', self.toggle_lock).setToolTip('锁定/解锁')
        toolbar.addAction('⚙️', self.show_settings).setToolTip('设置')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setApplicationName("海豚湾密码管理器")
    app.setApplicationVersion("1.0")
    
    # 设置应用图标
    app.setWindowIcon(QIcon('🐬'))
    
    window = DolphinPasswordManager()
    window.show()
    
    sys.exit(app.exec_())
