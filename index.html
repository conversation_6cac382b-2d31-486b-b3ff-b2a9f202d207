<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海豚湾密码管理器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 密码管理器主界面 -->
        <div id="password-manager" class="password-manager">
            <div class="header">
                <h1>🐬 海豚湾密码管理器</h1>
                <button id="add-password-btn" class="btn btn-primary">添加密码</button>
            </div>
            
            <div class="search-bar">
                <input type="text" id="search-input" placeholder="搜索网站或应用...">
            </div>
            
            <div class="password-list" id="password-list">
                <!-- 密码条目将在这里动态生成 -->
            </div>
        </div>

        <!-- 添加/编辑密码模态框 -->
        <div id="password-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modal-title">添加密码</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="password-form">
                    <div class="form-group">
                        <label for="site-name">网站/应用名称:</label>
                        <input type="text" id="site-name" required>
                    </div>
                    <div class="form-group">
                        <label for="username">用户名/邮箱:</label>
                        <input type="text" id="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">密码:</label>
                        <div class="password-input-group">
                            <input type="password" id="password" required>
                            <button type="button" id="generate-password" class="btn btn-secondary">生成密码</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="url">网站URL (可选):</label>
                        <input type="url" id="url">
                    </div>
                    <div class="form-group">
                        <label for="notes">备注 (可选):</label>
                        <textarea id="notes" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">保存</button>
                        <button type="button" id="cancel-btn" class="btn btn-secondary">取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 海豚湾登录模拟界面 -->
        <div id="dolphin-login" class="dolphin-login">
            <div class="login-container">
                <div class="login-left">
                    <div class="dolphin-animation">
                        <div class="dolphin">🐬</div>
                        <div class="stars">✨</div>
                    </div>
                    <h1 class="app-title">海豚湾</h1>
                </div>
                <div class="login-right">
                    <div class="success-message" id="success-message">
                        ✅ 退出登录成功!
                    </div>
                    <form id="login-form" class="login-form">
                        <div class="form-group">
                            <input type="text" id="login-username" placeholder="📧 <EMAIL>" class="form-input">
                            <button type="button" id="auto-fill-username" class="auto-fill-btn" title="自动填充用户名">👤</button>
                        </div>
                        <div class="form-group">
                            <input type="password" id="login-password" placeholder="密码" class="form-input">
                            <button type="button" id="auto-fill-password" class="auto-fill-btn" title="自动填充密码">🔑</button>
                            <button type="button" id="show-password" class="show-password-btn">👁️</button>
                        </div>
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="remember-password"> 记住密码
                            </label>
                            <a href="#" class="forgot-password">忘记密码</a>
                        </div>
                        <button type="submit" class="login-btn">登录</button>
                        <div class="register-link">
                            还没有账号? <a href="#">立即注册</a>
                        </div>
                    </form>
                </div>
            </div>
            <div class="switch-view">
                <button id="switch-to-manager" class="btn btn-primary">打开密码管理器</button>
            </div>
        </div>
    </div>

    <script src="crypto.js"></script>
    <script src="password-manager.js"></script>
    <script src="app.js"></script>
</body>
</html>
